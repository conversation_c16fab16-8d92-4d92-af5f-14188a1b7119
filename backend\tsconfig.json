{
  "compilerOptions": {
    "target": "ES2020",                       /* Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017', 'ES2018', 'ES2019', 'ES2020', or 'ESNEXT'. */
    "module": "commonjs",                     /* Specify module code generation: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', 'es2020', or 'ESNext'. */
    "lib": ["ES2020"],                        /* Specify library files to be included in the compilation. */
    "outDir": "../dist-backend",              /* Redirect output structure to the directory. */
    "rootDir": ".",                           /* Specify the root directory of input files. Use to control the output directory structure with --outDir. */
    "strict": true,                           /* Enable all strict type-checking options. */
    "esModuleInterop": true,                  /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */
    "skipLibCheck": true,                     /* Skip type checking of declaration files. */
    "forceConsistentCasingInFileNames": true, /* Disallow inconsistently-cased references to the same file. */
    "resolveJsonModule": true,                /* Include modules imported with .json extension */
    "moduleResolution": "node",
    "sourceMap": true,                        /* Generates corresponding '.map' file. */
    // "baseUrl": ".", /* Base directory to resolve non-absolute module names. */
    // "paths": {}, /* A series of entries which re-map imports to lookup locations relative to the 'baseUrl'. */
  },
  "include": [
    "server.ts",
    "../src/types.ts" 
  ],
  "exclude": [
    "node_modules",
    "../src", 
    "../dist-backend"
  ]
}
