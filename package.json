{"name": "devgenius-studio", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@google/genai": "^0.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1", "jszip": "^3.10.1", "express": "^4.19.2", "cors": "^2.8.5"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.8", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.11.19", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.0", "concurrently": "^8.2.2", "nodemon": "^3.0.3", "ts-node": "^10.9.2"}, "scripts": {"dev:frontend": "vite", "dev:backend": "nodemon --watch 'backend/**/*.ts' --exec 'npm run ts-node-esm' backend/server.ts", "ts-node-esm": "ts-node --esm", "dev": "concurrently \"npm:dev:frontend\" \"npm:dev:backend\"", "build:frontend": "tsc && vite build", "build:backend": "tsc -p backend/tsconfig.json", "build": "npm run build:frontend && npm run build:backend", "lint": "eslint . --ext ts,tsx,js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start:backend-dist": "node dist-backend/server.js", "start": "npm run build && concurrently \"vite preview --port 8080\" \"npm:start:backend-dist\""}, "eslintConfig": {"root": true, "env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks", "jsx-a11y"], "settings": {"react": {"version": "detect"}}, "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn"}, "overrides": [{"files": ["src/**/*.{ts,tsx,js,jsx}"], "plugins": ["react-refresh"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}]}}, {"files": ["*.js", "*.cjs", "tailwind.config.js", "postcss.config.js"], "env": {"node": true, "browser": false}, "rules": {"@typescript-eslint/no-var-requires": "off"}}]}}